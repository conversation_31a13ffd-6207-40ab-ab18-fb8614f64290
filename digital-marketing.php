<?php $MENU='digital'; require_once('header.php'); ?>

<style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #333;
        }

        /* Hero Section Styles */
        .hero-section {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding-top: 100px;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(0,0,0,0.05)"/><circle cx="80" cy="40" r="1" fill="rgba(0,0,0,0.05)"/><circle cx="40" cy="80" r="1" fill="rgba(0,0,0,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            pointer-events: none;
        }

        .hero-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .hero-text {
            flex: 1;
            max-width: 50%;
            z-index: 2;
        }

        .hero-text h1 {
            font-size: 3.5rem;
            font-weight: 700;
            color: #000;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .hero-text p {
            font-size: 1.1rem;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        /* Service Buttons */
        .service-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 30px 0;
        }

        .service-btn {
            background-color: transparent;
            border: 2px solid #000;
            color: #000;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .service-btn:hover {
            background-color: #000;
            color: #FFD700;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .hero-image {
            flex: 1;
            max-width: 50%;
            text-align: center;
        }

        .hero-image img {
            max-width: 100%;
            height: auto;
        }

        .connect-btn {
            background-color: #000;
            color: #fff;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
            transition: all 0.3s ease;
        }

        .connect-btn:hover {
            background-color: #333;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            color: #FFD700;
        }

        .connect-btn:active {
            transform: translateY(0);
        }

        /* Wave separator */
        .wave-separator {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
        }

        .wave-separator svg {
            position: relative;
            display: block;
            width: calc(100% + 1.3px);
            height: 100px;
        }

        .wave-separator .shape-fill {
            fill: #FFFFFF;
        }

        /* Content Sections */
        .content-section {
            padding: 80px 0;
            background-color: #fff;
        }

        /* Cloud wave separator */
        .cloud-wave-separator {
            position: relative;
            height: 100px;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            overflow: hidden;
        }

        .cloud-wave-separator svg {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
        }

        .cloud-wave-separator .shape-fill {
            fill: #ffffff;
        }

        /* Special styling for the "Be Found" section */
        .content-section.be-found-section {
            background: #ffffff;
            padding: 80px 0;
            position: relative;
        }

        .section-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section-content {
            display: flex;
            align-items: center;
            gap: 60px;
            margin-bottom: 60px;
        }

        .section-content.reverse {
            flex-direction: row-reverse;
        }

        .content-text {
            flex: 1;
        }
        
        /* Special styling for the "Be Found" section text */
        .be-found-section .content-text {
            position: relative;
            padding: 50px;
            border: 3px dotted #1E90FF;
            border-radius: 25px;
            background: rgba(30, 144, 255, 0.05);
            box-shadow: 0 4px 20px rgba(30, 144, 255, 0.1);
            overflow: hidden;
        }

        /* Cloud-like border effect */
        .be-found-section .content-text::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: 29px;
            background: linear-gradient(45deg,
                #1E90FF 0%,
                transparent 25%,
                #00BFFF 50%,
                transparent 75%,
                #1E90FF 100%);
            background-size: 20px 20px;
            z-index: -1;
            animation: cloudMove 6s ease-in-out infinite;
        }

        @keyframes cloudMove {
            0%, 100% {
                background-position: 0px 0px, 10px 10px;
                opacity: 0.8;
            }
            50% {
                background-position: 20px 20px, 30px 30px;
                opacity: 1;
            }
        }

        /* Additional cloud dots for more organic look */
        .be-found-section .content-text::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 27px;
            background:
                radial-gradient(circle at 20% 20%, #1E90FF 2px, transparent 2px),
                radial-gradient(circle at 80% 20%, #00BFFF 2px, transparent 2px),
                radial-gradient(circle at 40% 40%, #1E90FF 1px, transparent 1px),
                radial-gradient(circle at 60% 60%, #00BFFF 1px, transparent 1px),
                radial-gradient(circle at 20% 80%, #1E90FF 2px, transparent 2px),
                radial-gradient(circle at 80% 80%, #00BFFF 2px, transparent 2px);
            background-size: 40px 40px, 35px 35px, 25px 25px, 30px 30px, 45px 45px, 38px 38px;
            z-index: -1;
            animation: cloudFloat 8s ease-in-out infinite;
        }

        @keyframes cloudFloat {
            0%, 100% {
                transform: translateX(0px) translateY(0px);
            }
            25% {
                transform: translateX(2px) translateY(-1px);
            }
            50% {
                transform: translateX(-1px) translateY(2px);
            }
            75% {
                transform: translateX(1px) translateY(1px);
            }
        }

        .content-text h2 {
            font-size: 2.5rem;
            font-weight: 600;
            color: #FFB800;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        /* Special styling for the "Be Found" section heading */
        .be-found-section .content-text h2 {
            color: #FFB800;
            font-weight: 700;
            font-size: 2.8rem;
        }

        .content-text p {
            font-size: 2.2rem;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        /* Special styling for the "Be Found" section paragraph */
        .be-found-section .content-text p {
            color: #333;
            font-size: 1.9rem;
            line-height: 1.8;
            font-weight: 400;
            text-align:justify
        }

        .content-image {
            flex: 1;
            text-align: center;
            position: relative;
        }

        .content-image img {
            max-width: 100%;
            height: auto;
            border-radius: 20px;
        }

        /* Special styling for the "Be Found" section image container */
        .be-found-section .content-image {
            position: relative;
        }

        .be-found-section .content-image .image-container {
            position: relative;
            display: inline-block;
        }

        .be-found-section .content-image img {
            max-width: 100%;
            height: auto;
            border-radius: 20px;
        }

        /* Decorative icons around the image */
        .be-found-section .content-image .icon-decoration {
            position: absolute;
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #1E90FF;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            animation: float 3s ease-in-out infinite;
        }

        .be-found-section .content-image .icon-decoration:nth-child(2) {
            top: 20px;
            right: 20px;
            animation-delay: 0s;
        }

        .be-found-section .content-image .icon-decoration:nth-child(3) {
            top: 50%;
            right: -25px;
            transform: translateY(-50%);
            animation-delay: 1s;
        }

        .be-found-section .content-image .icon-decoration:nth-child(4) {
            bottom: 20px;
            right: 20px;
            animation-delay: 2s;
        }

        .be-found-section .content-image .icon-decoration:nth-child(5) {
            bottom: 20px;
            left: 20px;
            animation-delay: 1.5s;
        }

        .be-found-section .content-image .icon-decoration:nth-child(6) {
            top: 50%;
            left: -25px;
            transform: translateY(-50%);
            animation-delay: 0.5s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        /* Ensure the floating animation works with positioned elements */
        .be-found-section .content-image .icon-decoration:nth-child(3) {
            animation: float-right 3s ease-in-out infinite;
            animation-delay: 1s;
        }

        .be-found-section .content-image .icon-decoration:nth-child(6) {
            animation: float-left 3s ease-in-out infinite;
            animation-delay: 0.5s;
        }

        @keyframes float-right {
            0%, 100% {
                transform: translateY(-50%) translateX(0px);
            }
            50% {
                transform: translateY(-50%) translateX(-10px);
            }
        }

        @keyframes float-left {
            0%, 100% {
                transform: translateY(-50%) translateX(0px);
            }
            50% {
                transform: translateY(-50%) translateX(10px);
            }
        }

        /* Services Grid */
        .services-section {
            background-color: #f8f9fa;
            padding: 80px 0;
        }

        .services-title {
            text-align: center;
            margin-bottom: 60px;
        }

        .services-title h2 {
            font-size: 2.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }


        .service-card {
            background: #fff;
            padding: 40px 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .service-card .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: #000;
        }

        .service-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .service-card p {
            color: #666;
            line-height: 1.6;
        }

        /* Why Choose Section */
        .why-choose-section {
            padding: 80px 0;
            background-color: #fff;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }

        .benefit-item {
            display: flex;
            align-items: flex-start;
            gap: 20px;
        }

        .benefit-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: #000;
            flex-shrink: 0;
        }

        .benefit-content h4 {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .benefit-content p {
            color: #666;
            line-height: 1.6;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-content {
                flex-direction: column;
                text-align: center;
            }

            .hero-text, .hero-image {
                max-width: 100%;
            }

            .hero-text h1 {
                font-size: 2.5rem;
            }

            .section-content {
                flex-direction: column;
                gap: 40px;
            }

            .section-content.reverse {
                flex-direction: column;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }

            .benefits-grid {
                grid-template-columns: 1fr;
            }

            /* Responsive adjustments for the "Be Found" section */
            .be-found-section .content-text {
                padding: 30px 20px;
                margin-bottom: 30px;
            }

            .be-found-section .content-text h2 {
                font-size: 2.2rem;
            }

            .be-found-section .content-text p {
                font-size: 1.4rem;
            }

            .be-found-section .content-image .icon-decoration {
                width: 40px;
                height: 40px;
                font-size: 18px;
            }

            .be-found-section .content-image .icon-decoration:nth-child(3),
            .be-found-section .content-image .icon-decoration:nth-child(6) {
                display: none;
            }
        }
        </style>

    <!-- Hero Section -->
    <div class="hero-section">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Digital Marketing Services</h1>
                <p>Attain a top brand position with strategic & best digital marketing services in India from industry leaders. SureCLIQ is one of the leading digital marketing company you can trust on. Get comprehensive services package in one place</p>

                <!-- Service Buttons -->
                <div class="service-buttons">
                    <a href="#seo" class="service-btn">SEO</a>
                    <a href="#ppc" class="service-btn">PPC</a>
                    <a href="#smo" class="service-btn">SMO</a>
                    <a href="#orm" class="service-btn">ORM</a>
                </div>
                <div class="service-buttons">
                    <a href="#email" class="service-btn">Email Marketing</a>
                    <a href="#analytics" class="service-btn">Google Analytics</a>
                </div>

                <a href="https://name.surecliq.com/console/#signup" class="connect-btn">Connect Now</a>
            </div>

            <div class="hero-image">
                <img src="images/seo-optimization-concept-illustration 1.svg" alt="Digital Marketing Services">
            </div>
        </div>
        
    </div>


    <!-- Be Found Section -->
    <div class="content-section be-found-section">
        <div class="section-container">
            <div class="section-content">
                <div class="content-text">
                    <h2>Be Found, grow & accelerate business digitally-extensively</h2>
                    <p>Ensure the algorithm is just solving to balance success. It is also important to extend your entire execution space. Thus, map are benefits of 150-string digits in working schedule. As a result, we can use this approach with other examples that will help reduce external inputs and managing conflicts on steps balanced models. Typically, learning has helped and focuses on this rendering-based approach by our digital marketing experts. And application's path-based growth started.</p>
                </div>
                <div class="content-image">
                    <div class="image-container">
                        <img src="images/2112557_281984-P6M9HG-225 1.svg" alt="Digital Marketing Growth Illustration" />
                        <!-- Decorative Icons -->
                        <div class="icon-decoration">
                            <i class="fa fa-lightbulb-o"></i>
                        </div>
                        <div class="icon-decoration">
                            <i class="fa fa-bullseye"></i>
                        </div>
                        <div class="icon-decoration">
                            <i class="fa fa-check-circle"></i>
                        </div>
                        <div class="icon-decoration">
                            <i class="fa fa-file-text-o"></i>
                        </div>
                        <div class="icon-decoration">
                            <i class="fa fa-envelope-o"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Services Section -->
    <div class="services-section">
        <div class="section-container">
            <div class="services-title">
                <h2>360 - Degree Digital Marketing Services for companies of all Kinds</h2>
                <p>As you value-driven digital marketing agency in India, we are here to boost all your brands. Our professional team will engage your business. Our technical experts for your digital marketing performance and strategy from our best business solutions.</p>
            </div>

            <div class="services-grid">
                <div class="service-card">
                    <div class="icon">
                        <i class="fa fa-search"></i>
                    </div>
                    <h3>Search Engine Optimization</h3>
                    <p>Boost your organic search rankings and drive quality traffic to your website with our proven SEO strategies that deliver long-term results.</p>
                </div>

                <div class="service-card">
                    <div class="icon">
                        <i class="fa fa-bullhorn"></i>
                    </div>
                    <h3>Social Media Marketing</h3>
                    <p>Build a strong social presence and engage with your audience across all major social media platforms to increase brand awareness.</p>
                </div>

                <div class="service-card">
                    <div class="icon">
                        <i class="fa fa-envelope"></i>
                    </div>
                    <h3>Email Marketing</h3>
                    <p>Create personalized email campaigns that convert prospects into customers and keep existing customers engaged with your brand.</p>
                </div>

                <div class="service-card">
                    <div class="icon">
                        <i class="fa fa-mouse-pointer"></i>
                    </div>
                    <h3>PPC Advertising</h3>
                    <p>Get immediate visibility and results with our expertly managed pay-per-click campaigns across Google Ads and social platforms.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Why Choose Digital Marketing Section -->
    <div class="why-choose-section">
        <div class="section-container">
            <div class="services-title">
                <h2>Why Your Business Needs Digital Marketing Services?</h2>
            </div>

            <div class="benefits-grid">
                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fa fa-users"></i>
                    </div>
                    <div class="benefit-content">
                        <h4>Support brand value</h4>
                        <p>Digital marketing from social media marketing to everything builds the brand value and visibility. Social media is a powerful tool for brand building and customer engagement.</p>
                    </div>
                </div>

                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fa fa-chart-line"></i>
                    </div>
                    <div class="benefit-content">
                        <h4>Boost your relationships</h4>
                        <p>The modern buyer is not just concerned with services and needs but they also want to be connected with the brand. Digital marketing helps build stronger relationships.</p>
                    </div>
                </div>

                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fa fa-bullseye"></i>
                    </div>
                    <div class="benefit-content">
                        <h4>Drive more traffic</h4>
                        <p>Digital marketing helps drive to proper targeted traffic. Targeted traffic opportunities to create potential customers and increase sales.</p>
                    </div>
                </div>

                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fa fa-cogs"></i>
                    </div>
                    <div class="benefit-content">
                        <h4>Increase brand awareness</h4>
                        <p>These marketing methods allow you to see multiple aspects and use a complete brand to build business awareness and increase brand recognition.</p>
                    </div>
                </div>

                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fa fa-trophy"></i>
                    </div>
                    <div class="benefit-content">
                        <h4>Increase brand competition</h4>
                        <p>Digital marketing experts help you to understand competitive analysis to compete better with your competitors and gain market advantage.</p>
                    </div>
                </div>

                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fa fa-dollar-sign"></i>
                    </div>
                    <div class="benefit-content">
                        <h4>Increase conversion rates</h4>
                        <p>Attract potential leads, business conversions, communities and sales to your business products and services through effective digital marketing strategies.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Testimonials Section -->
    <div id="testimonials" class="section wb">
        <div class="container">
            <div class="section-title text-center">
                <h3>Happy Clients</h3>
                <p class="lead">We thanks for all our awesome testimonials! There are hundreds of our happy customers!
                    <br>Let's see what others say about SureCLIQ Digital Marketing Services!
                </p>
            </div><!-- end title -->

            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <div class="testi-carousel owl-carousel owl-theme">
                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Outstanding SEO Results!</h3>
                                <p class="lead">"SureCLIQ's digital marketing team helped us achieve first page rankings for our target keywords. Our organic traffic increased by 300% in just 6 months."</p>
                            </div>
                            <div class="testi-meta">
                                <h4>Sarah Johnson<small>- E-commerce Director</small></h4>
                            </div>
                        </div>

                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Excellent PPC Management!</h3>
                                <p class="lead">"Their PPC campaigns delivered exceptional ROI. We saw a 250% increase in qualified leads while reducing our cost per acquisition by 40%."</p>
                            </div>
                            <div class="testi-meta">
                                <h4>Michael Chen <small>- Marketing Manager</small></h4>
                            </div>
                        </div>

                        <div class="testimonial clearfix">
                            <div class="desc">
                                <h3><i class="fa fa-quote-left"></i> Social Media Success!</h3>
                                <p class="lead">"Our social media presence transformed completely. Engagement rates increased by 400% and we gained thousands of new followers across all platforms."</p>
                            </div>
                            <div class="testi-meta">
                                <h4>Emma Rodriguez <small>- Brand Manager</small></h4>
                            </div>
                        </div>
                    </div><!-- end carousel -->
                </div><!-- end col -->
            </div><!-- end row -->
        </div><!-- end container -->
    </div><!-- end section -->

<?php require_once('footer.php'); ?>

<script>
// Smooth scrolling for service buttons
document.querySelectorAll('.service-btn').forEach(btn => {
    btn.addEventListener('click', function(e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add animation on scroll
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe service cards and benefit items
document.querySelectorAll('.service-card, .benefit-item').forEach((el, index) => {
    el.style.opacity = '0';
    el.style.transform = 'translateY(30px)';
    el.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
    observer.observe(el);
});

// Add smooth hover effects
document.querySelectorAll('.service-btn').forEach(btn => {
    btn.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px) scale(1.05)';
    });

    btn.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});
</script>
